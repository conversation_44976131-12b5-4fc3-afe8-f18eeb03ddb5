<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
   <key>NSLocalNetworkUsageDescription</key>
<string>Reasoning for the user why you need this permission goes here</string>
<key>NSBonjourServices</key>
<array>
 <string>_http._tcp</string>
   <string>_everhome._tcp.</string>
</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Jackery Home</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Jackery Home</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tel</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>This application uses Bluetooth to communicate with the device, this application does not collect, store or record any data that communicates via Bluetooth.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>This app needs bluetooth access to link your local machine</string>
	<key>NSBonjourServices</key>
	<array>
		<string>mqtt.tcp</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to scan QR codes , take photos and upload feedback images</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Looking for local tcp Bonjour service</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This application uses location permissions to obtain currently connected Wi-Fi information. This application does not collect, store or record any location data.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs location access to get your position</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to get your position in app usage</string>
	<key>NSMotionUsageDescription</key>
	<string>This app needs Use Motion when take photos maybe ratote the screen</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo access to take photos and upload feedback images</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>colorMode</key>
	<string>$(colorMode)</string>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>
