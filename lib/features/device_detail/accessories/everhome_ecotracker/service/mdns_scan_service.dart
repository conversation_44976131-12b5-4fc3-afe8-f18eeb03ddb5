import 'dart:async';

import 'package:flutter_basic/platform/utils/log_utils.dart';
import 'package:multicast_dns/multicast_dns.dart';

import '../model/ecotracker_device_model.dart';

class MdnsScanService {
  static final MdnsScanService _instance = MdnsScanService._internal();

  factory MdnsScanService() => _instance;

  MdnsScanService._internal();

  MDnsClient? _client;
  Timer? _scanTimer;
  final StreamController<List<EcoTrackerDeviceModel>> _devicesController =
      StreamController<List<EcoTrackerDeviceModel>>.broadcast();
  final Map<String, EcoTrackerDeviceModel> _discoveredDevices = {};

  // EcoTracker 设备的 mDNS 服务名称
  static const String _ecoTrackerServiceName = '_everhome._tcp.';

  Stream<List<EcoTrackerDeviceModel>> get devicesStream =>
      _devicesController.stream;

  List<EcoTrackerDeviceModel> get currentDevices =>
      _discoveredDevices.values.toList();

  /// 开始扫描设备
  Future<void> startScan(
      {Duration timeout = const Duration(seconds: 30)}) async {
    try {
      logger.d('开始 mDNS 扫描 EcoTracker 设备');

      // 停止之前的扫描
      await stopScan();

      // 创建新的 mDNS 客户端
      _client = MDnsClient();
      await _client!.start();

      // 清空之前的发现结果
      _discoveredDevices.clear();
      _notifyDevicesUpdate();

      // 开始扫描
      _scanForDevices();

      // 设置超时定时器
      _scanTimer = Timer(timeout, () async {
        logger.d('mDNS 扫描超时，停止扫描');
        await stopScan();
      });
    } catch (e) {
      logger.e('启动 mDNS 扫描失败: $e');
      rethrow;
    }
  }

  /// 停止扫描设备
  Future<void> stopScan() async {
    try {
      _scanTimer?.cancel();
      _scanTimer = null;

      if (_client != null) {
        _client!.stop();
        _client = null;
      }

      logger.d('mDNS 扫描已停止');
    } catch (e) {
      logger.e('停止 mDNS 扫描失败: $e');
    }
  }

  /// 扫描设备的核心逻辑
  void _scanForDevices() {
    if (_client == null) return;

    // 查找 PTR 记录
    _client!
        .lookup<PtrResourceRecord>(
      ResourceRecordQuery.serverPointer(_ecoTrackerServiceName),
    )
        .listen((PtrResourceRecord ptr) {
      logger.d('发现 PTR 记录: ${ptr.domainName}');
      _handlePtrRecord(ptr);
    }, onError: (error) {
      logger.e('PTR 查询错误: $error');
    });
  }

  /// 处理 PTR 记录
  void _handlePtrRecord(PtrResourceRecord ptr) {
    if (_client == null) return;

    // 获取 SRV 记录（包含端口和主机名）
    _client!
        .lookup<SrvResourceRecord>(
      ResourceRecordQuery.service(ptr.domainName),
    )
        .listen((SrvResourceRecord srv) {
      logger.d('发现 SRV 记录: ${srv.target}:${srv.port}');
      _handleSrvRecord(ptr.domainName, srv);
    }, onError: (error) {
      logger.e('SRV 查询错误: $error');
    });

    // 获取 TXT 记录（包含设备信息）
    _client!
        .lookup<TxtResourceRecord>(
      ResourceRecordQuery.text(ptr.domainName),
    )
        .listen((TxtResourceRecord txt) {
      logger.d('发现 TXT 记录: ${txt.text}');
      _handleTxtRecord(ptr.domainName, txt);
    }, onError: (error) {
      logger.e('TXT 查询错误: $error');
    });
  }

  /// 处理 SRV 记录
  void _handleSrvRecord(String serviceName, SrvResourceRecord srv) {
    // 解析 IP 地址
    _resolveIpAddress(srv.target).then((ipAddress) {
      if (ipAddress != null) {
        _updateDeviceInfo(serviceName, ipAddress: ipAddress, port: srv.port);
      }
    }).catchError((error) {
      logger.e('解析 IP 地址失败: $error');
    });
  }

  /// 处理 TXT 记录
  void _handleTxtRecord(String serviceName, TxtResourceRecord txt) {
    final txtRecords = <String, String>{};

    // 处理 TXT 记录数据
    try {
      if (txt.text is List) {
        for (final data in txt.text as List) {
          if (data is List<int>) {
            final text = String.fromCharCodes(data);
            final parts = text.split('=');
            if (parts.length == 2) {
              txtRecords[parts[0]] = parts[1];
            }
          }
        }
      }
    } catch (e) {
      logger.e('处理 TXT 记录失败: $e');
    }

    _updateDeviceInfo(serviceName, txtRecords: txtRecords);
  }

  /// 解析主机名到 IP 地址
  Future<String?> _resolveIpAddress(String hostname) async {
    try {
      if (_client == null) return null;

      // 查找 IPv4 地址记录
      await for (final record in _client!.lookup<IPAddressResourceRecord>(
        ResourceRecordQuery.addressIPv4(hostname),
      )) {
        return record.address.address;
      }

      // 如果没有找到 IPv4，尝试 IPv6
      await for (final record in _client!.lookup<IPAddressResourceRecord>(
        ResourceRecordQuery.addressIPv6(hostname),
      )) {
        return record.address.address;
      }
    } catch (e) {
      logger.e('解析 IP 地址失败: $e');
    }

    return null;
  }

  /// 更新设备信息
  void _updateDeviceInfo(
    String serviceName, {
    String? ipAddress,
    int? port,
    Map<String, String>? txtRecords,
  }) {
    final deviceId = serviceName.split('.').first;

    if (_discoveredDevices.containsKey(deviceId)) {
      // 更新现有设备
      final existingDevice = _discoveredDevices[deviceId]!;
      _discoveredDevices[deviceId] = existingDevice.copyWith(
        ipAddress: ipAddress ?? existingDevice.ipAddress,
        port: port ?? existingDevice.port,
        additionalInfo: txtRecords != null
            ? {...?existingDevice.additionalInfo, ...txtRecords}
            : existingDevice.additionalInfo,
      );
    } else if (ipAddress != null && port != null) {
      // 创建新设备
      final device = EcoTrackerDeviceModel.fromMdnsRecord(
        serviceName: serviceName,
        hostname: serviceName,
        ipAddress: ipAddress,
        port: port,
        txtRecords: txtRecords,
      );
      _discoveredDevices[deviceId] = device;
    }

    _notifyDevicesUpdate();
  }

  /// 通知设备列表更新
  void _notifyDevicesUpdate() {
    if (!_devicesController.isClosed) {
      _devicesController.add(currentDevices);
    }
  }

  /// 清空发现的设备
  void clearDevices() {
    _discoveredDevices.clear();
    _notifyDevicesUpdate();
  }

  /// 释放资源
  void dispose() {
    stopScan();
    _devicesController.close();
  }
}
